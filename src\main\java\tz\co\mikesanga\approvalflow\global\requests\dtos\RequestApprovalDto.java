package tz.co.mikesanga.approvalflow.global.requests.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.global.requests.entities.Request;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RequestApprovalDto {

    @NotBlank(message = "Request UUID is required")
    private String requestUuid;

    @NotNull(message = "Approval decision is required")
    private Request.RequestStatus decision; // APPROVED or REJECTED

    @Size(max = 500, message = "Approval comment cannot exceed 500 characters")
    private String comment;
}
