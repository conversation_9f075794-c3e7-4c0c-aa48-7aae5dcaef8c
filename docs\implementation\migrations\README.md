# Migration Guides

Version-specific migration instructions and breaking changes for the Approval Flow API.

## 📋 Migration Overview

This section contains migration guides for upgrading between different versions of the Approval Flow API system. Each migration guide includes:

- Breaking changes
- Database schema updates
- Configuration changes
- API changes
- Step-by-step migration instructions

## 🔄 Current Version: 1.0.0

### Latest Features
- User Management with unified save pattern
- Request Management with approval workflow
- Role-based access control
- JWT authentication
- GraphQL API with SPQR

## 📚 Migration History

### Version 1.0.0 (Current)
- **Release Date**: Current
- **Status**: Stable
- **Breaking Changes**: None (initial release)

#### New Features
- Complete user management system
- Request approval workflow
- Role and permission management
- GraphQL API implementation
- JWT-based authentication

#### Database Schema
- Initial schema with all core tables
- User accounts, roles, permissions, requests
- Proper relationships and constraints

## 🚀 Future Migration Planning

### Planned Changes
Future versions may include:
- Database migration tools (Flyway/Liquibase)
- API versioning strategy
- Schema evolution support
- Backward compatibility guidelines

### Migration Strategy
When new versions are released, migration guides will include:

1. **Pre-Migration Checklist**
   - Backup procedures
   - Compatibility checks
   - Dependency updates

2. **Database Migrations**
   - Schema changes
   - Data transformations
   - Index updates

3. **Configuration Updates**
   - New properties
   - Deprecated settings
   - Security changes

4. **API Changes**
   - New endpoints
   - Modified responses
   - Deprecated features

5. **Post-Migration Verification**
   - Testing procedures
   - Rollback instructions
   - Performance validation

## 🛠️ Migration Tools

### Database Backup
```bash
# PostgreSQL backup
pg_dump -h localhost -U username -d approval_flow > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore if needed
psql -h localhost -U username -d approval_flow < backup_file.sql
```

### Configuration Backup
```bash
# Backup configuration files
cp src/main/resources/application.properties application.properties.backup
cp -r src/main/resources/config/ config.backup/
```

### Application Backup
```bash
# Backup current application
cp build/libs/approval-flow-*.jar approval-flow-backup.jar
```

## 📖 Migration Best Practices

### Before Migration
1. **Create Backups**
   - Database backup
   - Configuration backup
   - Application backup

2. **Test in Development**
   - Run migration in dev environment
   - Verify all functionality
   - Test rollback procedures

3. **Review Changes**
   - Read migration guide thoroughly
   - Understand breaking changes
   - Plan downtime if needed

### During Migration
1. **Follow Steps Sequentially**
   - Don't skip steps
   - Verify each step completion
   - Monitor for errors

2. **Monitor System**
   - Watch application logs
   - Check database connections
   - Verify API responses

### After Migration
1. **Verify Functionality**
   - Test critical workflows
   - Check user authentication
   - Validate data integrity

2. **Performance Check**
   - Monitor response times
   - Check database performance
   - Verify memory usage

3. **Update Documentation**
   - Update deployment docs
   - Notify team of changes
   - Update monitoring configs

## 🚨 Rollback Procedures

### Emergency Rollback
If migration fails:

1. **Stop Application**
   ```bash
   # Stop the application
   pkill -f approval-flow
   ```

2. **Restore Database**
   ```bash
   # Drop current database
   dropdb approval_flow
   
   # Recreate database
   createdb approval_flow
   
   # Restore from backup
   psql -h localhost -U username -d approval_flow < backup_file.sql
   ```

3. **Restore Application**
   ```bash
   # Use previous version
   cp approval-flow-backup.jar build/libs/approval-flow-current.jar
   
   # Restore configuration
   cp application.properties.backup src/main/resources/application.properties
   ```

4. **Restart Application**
   ```bash
   # Start previous version
   java -jar build/libs/approval-flow-current.jar
   ```

## 📞 Migration Support

### Pre-Migration Checklist
- [ ] Read complete migration guide
- [ ] Create all necessary backups
- [ ] Test migration in development
- [ ] Plan maintenance window
- [ ] Notify users of potential downtime
- [ ] Prepare rollback plan

### Post-Migration Checklist
- [ ] Verify application starts successfully
- [ ] Test user authentication
- [ ] Verify API endpoints
- [ ] Check database integrity
- [ ] Monitor performance metrics
- [ ] Update documentation
- [ ] Notify users of completion

### Getting Help
If you encounter issues during migration:

1. **Check Logs**
   ```bash
   # Application logs
   tail -f logs/application.log
   
   # Database logs
   tail -f /var/log/postgresql/postgresql.log
   ```

2. **Verify Configuration**
   ```bash
   # Check configuration
   grep -v '^#' src/main/resources/application.properties
   ```

3. **Test Connectivity**
   ```bash
   # Test database connection
   psql -h localhost -U username -d approval_flow -c "SELECT 1;"
   
   # Test API
   curl http://localhost:8082/actuator/health
   ```

## 📚 Related Documentation

- [Setup Guide](../../setup/README.md) - Installation and configuration
- [Troubleshooting](../troubleshooting.md) - Common issues and solutions
- [Development Guide](../../development/README.md) - Development environment
- [API Documentation](../../api/README.md) - API reference

---

*Migration guides will be added here as new versions are released. Always backup your data before performing migrations.*
