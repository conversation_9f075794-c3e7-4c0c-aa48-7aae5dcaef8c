package tz.co.mikesanga.approvalflow.uaa.services;


import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.uaa.repositories.UserAccountRepository;
import tz.co.mikesanga.approvalflow.utils.Constants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserAccountServiceImpl implements UserAccountService, TableSeeder {

    private final UserAccountRepository userAccountRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public Optional<UserAccount> getOptionalByUsername(String username) {
        return username != null && !username.isEmpty() ? userAccountRepository.findFirstByUsername(username) : Optional.empty();
    }

    @Override
    public Optional<UserAccount> getOptionalByEmail(String email) {
        return email != null && !email.isEmpty() ? userAccountRepository.findFirstByEmail(email) : Optional.empty();
    }


    @Override
    public void seed() {
        List<UserAccount> userAccounts = new ArrayList<>() {
            {
                add(UserAccount.builder()
                        .username(Constants.SuperAdmin.USERNAME)
                        .password(passwordEncoder.encode(Constants.SuperAdmin.PASSWORD))
                        .fullName(Constants.SuperAdmin.FULL_NAME)
                        .email("<EMAIL>") // Add default email for super admin
                        .status(UserAccount.UserStatus.ACTIVE)
                        .build());
            }
        };
        for (UserAccount userAccount : userAccounts) {
            Optional<UserAccount> optional = getOptionalByUsername(userAccount.getUsername());
            if (optional.isPresent()) {
                continue;
            }
            userAccountRepository.save(userAccount);
        }
    }

    @Override
    @Transactional
    public void updateLastLogin(String username, LocalDateTime loginTime) {
        Optional<UserAccount> userOpt = getOptionalByUsername(username);
        if (userOpt.isPresent()) {
            UserAccount user = userOpt.get();
            user.setLastLogin(loginTime);
            userAccountRepository.save(user);
        }
    }

}
