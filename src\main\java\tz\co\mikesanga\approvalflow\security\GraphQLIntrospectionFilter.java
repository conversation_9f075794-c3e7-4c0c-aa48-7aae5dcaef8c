package tz.co.mikesanga.approvalflow.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
public class GraphQLIntrospectionFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if ("/graphql".equals(request.getRequestURI())) {
            String query = request.getParameter("query");
            if (query != null && query.contains("__schema")) { // Introspection query detection
                filterChain.doFilter(request, response); // Allow without authentication
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
}