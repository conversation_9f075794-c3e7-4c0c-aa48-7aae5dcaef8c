spring.application.name=approval-flow

server.port=${SERVER_PORT:8082}

spring.datasource.url=jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:approval_flow}
spring.datasource.username=${DB_USER:postgres}
spring.datasource.password=${DB_PASS:mike25027}
spring.jpa.hibernate.ddl-auto=${DB_DDL:update}
spring.datasource.driver-class-name=org.postgresql.Driver

security.jwt.secret-key=242FC6B39EB1FA126F86C2B21F38E

graphql.spqr.gui.enabled=true
graphql.spqr.gui.endpoint=/gui
graphql.spqr.gui.target-endpoint=/gql
graphql.spqr.http.endpoint=/graphql


logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.io.leangen.graphql=DEBUG
