package tz.co.mikesanga.approvalflow.uaa.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserResponseDto {

    private String uuid;
    private String username;
    private String fullName;
    private String email;
    private String phoneCode;
    private String phone;
    private UserAccount.UserStatus status;
    private LocalDateTime lastLogin;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<RoleResponseDto> roles;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleResponseDto {
        private String uuid;
        private String name;
        private String displayName;
        private String description;
    }
}
