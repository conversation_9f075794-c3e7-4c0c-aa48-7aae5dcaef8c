package tz.co.mikesanga.approvalflow.uaa.services;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.repositories.PermissionRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService, TableSeeder {

    private final PermissionRepository permissionRepository;

    @Override
    public void seed() {
        log.info("Seeding permissions...");
        List<Permission> permissions = new ArrayList<Permission>() {
            {
                // dashboard
                add(new Permission("ROLE_DASHBOARD_VIEW", "Can View Dashboard", "DASHBOARD"));

                // roles
                add(new Permission("ROLE_ROLES_VIEW", "Can View Roles", "UAA"));
                add(new Permission("ROLE_ROLES_ADD", "Can Add New Role", "UAA"));
                add(new Permission("ROLE_ROLES_EDIT", "Can Edit Role", "UAA"));
                add(new Permission("ROLE_ROLES_DELETE", "Can Delete Role", "UAA"));

                // users
                add(new Permission("ROLE_USERS_VIEW", "Can View Users", "UAA"));
                add(new Permission("ROLE_USERS_ADD", "Can Add New User", "UAA"));
                add(new Permission("ROLE_USERS_EDIT", "Can Edit User", "UAA"));
                add(new Permission("ROLE_USERS_DELETE", "Can Delete User", "UAA"));
                add(new Permission("ROLE_USERS_DEACTIVATE", "Can Deactivate/Reactivate User", "UAA"));
                add(new Permission("ROLE_USERS_ASSIGN_ROLES", "Can Assign Roles to User", "UAA"));

                // requests
                add(new Permission("ROLE_REQUESTS_CREATE", "Can Create Approval Requests", "REQUESTS"));
                add(new Permission("ROLE_REQUESTS_VIEW", "Can View Approval Requests", "REQUESTS"));
                add(new Permission("ROLE_REQUESTS_VIEW_ALL", "Can View All Approval Requests", "REQUESTS"));
                add(new Permission("ROLE_REQUESTS_APPROVE", "Can Approve/Reject Requests", "REQUESTS"));
                add(new Permission("ROLE_REQUESTS_EDIT", "Can Edit Own Requests", "REQUESTS"));
                add(new Permission("ROLE_REQUESTS_DELETE", "Can Delete Own Requests", "REQUESTS"));

                // expenses
                add(new Permission("ROLE_EXPENSES_CREATE", "Can Create Expense Reports", "EXPENSES"));
                add(new Permission("ROLE_EXPENSES_VIEW", "Can View Own Expense Reports", "EXPENSES"));
                add(new Permission("ROLE_EXPENSES_VIEW_ALL", "Can View All Expense Reports", "EXPENSES"));
                add(new Permission("ROLE_EXPENSES_EDIT", "Can Edit Own Expense Reports", "EXPENSES"));
                add(new Permission("ROLE_EXPENSES_DELETE", "Can Delete Own Expense Reports", "EXPENSES"));
                add(new Permission("ROLE_EXPENSES_APPROVE", "Can Approve/Reject Expense Reports", "EXPENSES"));

                // profile
                add(new Permission("ROLE_PROFILE_VIEW", "Can View Own Profile", "PROFILE"));
                add(new Permission("ROLE_PROFILE_EDIT", "Can Edit Own Profile", "PROFILE"));

                // settings
                add(new Permission("ROLE_SETTINGS_VIEW", "Can View System Settings", "SETTINGS"));
                add(new Permission("ROLE_SETTINGS_EDIT", "Can Edit System Settings", "SETTINGS"));

            }
        };


        List<Permission> savedPermissions = new ArrayList<>();

        for (Permission permission : permissions) {
            Optional<Permission> optionalPermission = permissionRepository.findPermissionByName(permission.getName());
            Permission existingPermission = optionalPermission.orElse(new Permission());

            existingPermission.setGroupName(permission.getGroupName());
            existingPermission.setName(permission.getName());
            existingPermission.setDescription(permission.getDescription());
            savedPermissions.add(permissionRepository.save(existingPermission));
        }

        List<Long> ids = savedPermissions.stream().map(Permission::getId).collect(Collectors.toList());
        permissionRepository.deleteRolePermissionByPermissionIdNotIn(ids);
        permissionRepository.deleteAllByIdNotIn(ids);


    }

    @Override
    public List<String> getPermissionsByRoleNames(List<String> list) {
        log.info("Getting permissions by role names... {}", list);
        return permissionRepository.getPermissionsByRoleNames(list);
    }
}


