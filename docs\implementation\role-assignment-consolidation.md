# UserManagement Service - Role Assignment Consolidation

This document describes the consolidation of role assignment functionality into the unified `saveUser` method, eliminating the separate `assignRolesToUser` endpoint for a cleaner, more atomic API.

## Overview

The UserManagement service has been simplified by removing the separate role assignment functionality and ensuring all role assignments are handled atomically within the `saveUser` method during user creation or updates.

## Changes Made

### 1. Removed Separate Role Assignment Endpoint
**Files Modified**:
- `UserManagementController.java` - Removed `assignRolesToUser` mutation
- `UserManagementService.java` - Removed `assignRolesToUser` method
- `UserManagementServiceImpl.java` - Removed `assignRolesToUser` implementation

### 2. Removed UserRoleAssignmentDto
**File Removed**: `UserRoleAssignmentDto.java`
- No longer needed since role assignment is handled via `roleUuids` field in `UserCreateRequestDto`

### 3. Updated Documentation
**File Modified**: `USER_MANAGEMENT_FEATURE.md`
- Removed references to separate role assignment endpoint
- Updated examples to show role assignment within `saveUser`
- Clarified that role assignments are atomic operations
- Updated permission descriptions

## Benefits of Consolidation

### 1. **Atomic Operations**
- ✅ Role assignments are now part of the same transaction as user creation/updates
- ✅ No risk of partial updates (user saved but roles not assigned)
- ✅ Better data consistency and integrity

### 2. **Simplified API**
- ✅ Single endpoint for all user operations including role management
- ✅ Reduced API surface area
- ✅ Easier to understand and use

### 3. **Consistent Architecture**
- ✅ Follows the same pattern as other consolidated save methods
- ✅ Matches RoleService and RequestService patterns
- ✅ Unified approach across the codebase

### 4. **Better Security**
- ✅ Role assignments are governed by the same permissions as user creation/editing
- ✅ No separate permission needed for role assignment
- ✅ Simplified permission model

## Role Assignment Implementation

### In UserCreateRequestDto:
```java
private List<String> roleUuids;  // Optional role assignments
```

### In UserManagementServiceImpl.saveUser():
```java
// Handle roles assignment
if (userDto.getRoleUuids() != null && !userDto.getRoleUuids().isEmpty()) {
    List<Role> roles = new ArrayList<>();
    for (String roleUuid : userDto.getRoleUuids()) {
        Optional<Role> roleOpt = roleRepository.findFirstByUuid(UUID.fromString(roleUuid));
        if (roleOpt.isPresent()) {
            roles.add(roleOpt.get());
        } else {
            log.warn("Role with UUID {} not found", roleUuid);
        }
    }
    userAccount.setRoles(roles);
}
```

## API Usage Examples

### Create User with Roles:
```graphql
mutation {
  saveUser(userDto: {
    username: "newuser"
    password: "securepassword123"
    fullName: "New User"
    email: "<EMAIL>"
    roleUuids: ["role-uuid-1", "role-uuid-2"]
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
      roles {
        uuid
        name
        displayName
      }
    }
  }
}
```

### Update User Roles:
```graphql
mutation {
  saveUser(userDto: {
    uuid: "existing-user-uuid"
    username: "existinguser"
    fullName: "Updated Name"
    email: "<EMAIL>"
    roleUuids: ["role-uuid-1", "role-uuid-3"]  # Completely replaces existing roles
  }) {
    status
    code
    data {
      uuid
      roles {
        uuid
        name
        displayName
      }
    }
  }
}
```

## Role Assignment Behavior

### Create Operation:
- ✅ **Optional**: Roles can be assigned during user creation
- ✅ **Validation**: Invalid role UUIDs are logged but don't fail the operation
- ✅ **Default**: If no roles provided, user is created without roles

### Update Operation:
- ✅ **Complete Replacement**: Providing `roleUuids` completely replaces existing roles
- ✅ **Optional**: If `roleUuids` not provided, existing roles are preserved
- ✅ **Clear Roles**: Providing empty `roleUuids` array removes all roles
- ✅ **Atomic**: Role updates happen in the same transaction as user updates

## Permission Model

### Before (Separate Endpoints):
- `ROLE_USERS_ADD` - For creating users
- `ROLE_USERS_EDIT` - For updating users
- `ROLE_USERS_ASSIGN_ROLES` - For assigning roles (separate operation)

### After (Consolidated):
- `ROLE_USERS_ADD` - For creating users (includes role assignment)
- `ROLE_USERS_EDIT` - For updating users (includes role assignment)

**Simplified Permission Check**:
```java
@PreAuthorize("hasAnyRole('ROLE_USERS_ADD', 'ROLE_USERS_EDIT')")
```

## Validation and Error Handling

### Role Assignment Validation:
- ✅ **Invalid UUIDs**: Logged as warnings, don't fail the operation
- ✅ **Non-existent Roles**: Skipped with warning log
- ✅ **Empty Arrays**: Removes all roles from user
- ✅ **Null Values**: Preserves existing roles

### Error Scenarios:
- Invalid role UUIDs are handled gracefully
- User creation/update continues even if some roles are invalid
- Detailed logging for troubleshooting

## Migration Notes

### For Existing API Consumers:
1. **Replace `assignRolesToUser` calls** with `saveUser` calls including the user's UUID
2. **Include all user fields** in the `saveUser` call (not just roles)
3. **Update permission checks** to use `ROLE_USERS_EDIT` instead of `ROLE_USERS_ASSIGN_ROLES`

### Example Migration:
```graphql
# OLD: Separate role assignment
mutation {
  assignRolesToUser(roleAssignment: {
    userUuid: "user-uuid"
    roleUuids: ["role1", "role2"]
  })
}

# NEW: Integrated role assignment
mutation {
  saveUser(userDto: {
    uuid: "user-uuid"
    username: "existinguser"
    fullName: "User Name"
    email: "<EMAIL>"
    roleUuids: ["role1", "role2"]
  })
}
```

## Summary

The consolidation of role assignment into the `saveUser` method provides:

- ✅ **Better atomicity** - All user operations in single transaction
- ✅ **Simplified API** - One endpoint for all user management
- ✅ **Consistent patterns** - Matches other service implementations
- ✅ **Improved security** - Unified permission model
- ✅ **Better maintainability** - Less code to maintain

This change aligns the UserManagement service with the established patterns in the codebase and provides a more robust, atomic approach to user and role management.
