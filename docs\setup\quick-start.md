# Quick Start Guide

Get the Approval Flow API up and running in minutes.

## ⚡ 5-Minute Setup

### Prerequisites
- Java 17+
- PostgreSQL running
- Git

### Step 1: Clone and Setup
```bash
# Clone repository
git clone <repository-url>
cd approval-flow-be

# Create database
createdb approval_flow
```

### Step 2: Configure
```bash
# Copy configuration template
cp src/main/resources/application.properties.example src/main/resources/application.properties

# Edit configuration (minimal required changes)
nano src/main/resources/application.properties
```

**Minimal Configuration:**
```properties
spring.datasource.url=**********************************************
spring.datasource.username=your_db_user
spring.datasource.password=your_db_password
jwt.secret=your-very-secure-256-bit-secret-key-here-minimum-32-chars
```

### Step 3: Run
```bash
# Start the application
./gradlew bootRun
```

### Step 4: Verify
- **API**: http://localhost:8082/graphql
- **GraphQL GUI**: http://localhost:8082/gui
- **Health**: http://localhost:8082/actuator/health

## 🚀 First API Call

### 1. Get Authentication Token
```bash
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "username": "admin",
    "fullName": "System Administrator"
  }
}
```

### 2. Test GraphQL API
```bash
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "query": "query { listUsers { status code dataList { uuid username fullName } } }"
  }'
```

## 🎯 Common Use Cases

### Create a User
```graphql
mutation {
  saveUser(userDto: {
    username: "johndoe"
    fullName: "John Doe"
    email: "<EMAIL>"
    roleUuids: ["user-role-uuid"]
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
    }
  }
}
```

### Create a Request
```graphql
mutation {
  saveRequest(requestDto: {
    title: "Vacation Request"
    description: "Requesting 5 days vacation"
  }) {
    status
    code
    data {
      uuid
      title
      status
      requestedDate
    }
  }
}
```

### List My Requests
```graphql
query {
  getMyRequests(filter: {
    page: 0
    size: 10
    sortBy: "requestedDate"
    sortDirection: "DESC"
  }) {
    status
    code
    dataList {
      uuid
      title
      status
      requestedDate
    }
    extras
  }
}
```

### Approve a Request (Admin)
```graphql
mutation {
  approveRequest(approvalDto: {
    requestUuid: "request-uuid-here"
    decision: APPROVED
    comment: "Approved for requested dates"
  }) {
    status
    code
    data {
      uuid
      status
      approvedDate
      approvalComment
    }
  }
}
```

## 🔧 Using GraphQL GUI

1. **Open GUI**: Navigate to http://localhost:8082/gui
2. **Set Headers**: Add Authorization header:
   ```
   {
     "Authorization": "Bearer YOUR_TOKEN_HERE"
   }
   ```
3. **Explore Schema**: Use the schema explorer on the right
4. **Run Queries**: Type queries in the left panel and click play

### Sample Queries to Try

#### Get All Available Roles
```graphql
query {
  listRoles {
    status
    code
    dataList {
      uuid
      name
      displayName
      permissions {
        name
      }
    }
  }
}
```

#### Search Users
```graphql
query {
  listUsers(filter: {
    search: "john"
    status: ACTIVE
    page: 0
    size: 5
  }) {
    status
    code
    dataList {
      uuid
      username
      fullName
      email
      status
    }
  }
}
```

## 🐳 Docker Quick Start

### Using Docker Compose
```bash
# Clone repository
git clone <repository-url>
cd approval-flow-be

# Start with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f app
```

**docker-compose.yml:**
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: approval_flow
      POSTGRES_USER: approval_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"

  app:
    build: .
    ports:
      - "8082:8082"
    depends_on:
      - postgres
    environment:
      SPRING_DATASOURCE_URL: *********************************************
      SPRING_DATASOURCE_USERNAME: approval_user
      SPRING_DATASOURCE_PASSWORD: secure_password
      JWT_SECRET: your-very-secure-256-bit-secret-key-here-minimum-32-chars
```

## 🔒 Security Quick Setup

### Change Default Admin Password
```graphql
mutation {
  saveUser(userDto: {
    uuid: "admin-user-uuid"
    password: "new_secure_password"
  }) {
    status
    code
  }
}
```

### Create Regular User
```graphql
mutation {
  saveUser(userDto: {
    username: "regularuser"
    fullName: "Regular User"
    email: "<EMAIL>"
    # password will default to "ChangeMe123!"
  }) {
    status
    code
    data {
      uuid
      username
    }
  }
}
```

## 📊 Monitoring Quick Setup

### Health Checks
```bash
# Application health
curl http://localhost:8082/actuator/health

# Database health
curl http://localhost:8082/actuator/health/db
```

### Basic Metrics
```bash
# JVM metrics
curl http://localhost:8082/actuator/metrics/jvm.memory.used

# HTTP metrics
curl http://localhost:8082/actuator/metrics/http.server.requests
```

## 🧪 Testing Quick Start

### Run Tests
```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests UserManagementServiceTest

# Run with coverage
./gradlew test jacocoTestReport
```

### Manual API Testing
Use the testing guides for comprehensive scenarios:
- [User Management Testing](../api/user-management/testing.md)
- [Request Management Testing](../api/request-management/testing.md)

## 🚨 Troubleshooting Quick Fixes

### Common Issues

#### Application Won't Start
```bash
# Check Java version
java -version  # Should be 17+

# Check port availability
lsof -i :8082

# Check database connection
psql -h localhost -U your_user -d approval_flow
```

#### Authentication Issues
```bash
# Verify JWT secret length (must be 256+ bits)
echo "your-jwt-secret" | wc -c  # Should be 32+ characters

# Check token format
echo "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### Database Issues
```sql
-- Check if database exists
\l

-- Check if tables are created
\dt

-- Check user permissions
\du
```

## 📚 Next Steps

Once you have the basic setup working:

1. **[Complete Setup Guide](README.md)** - Detailed configuration options
2. **[API Documentation](../api/README.md)** - Explore all available endpoints
3. **[Development Guide](../development/README.md)** - Set up development environment
4. **[Testing Documentation](../testing/README.md)** - Comprehensive testing

## 🎉 You're Ready!

Your Approval Flow API is now running and ready to use. The system includes:

- ✅ User management with role-based access control
- ✅ Request approval workflow
- ✅ GraphQL API with interactive GUI
- ✅ JWT-based authentication
- ✅ Comprehensive permission system

Start exploring the API using the GraphQL GUI at http://localhost:8082/gui!

---

*Need help? Check the [Troubleshooting Guide](../implementation/troubleshooting.md) or [Full Documentation](../README.md).*
