package tz.co.mikesanga.approvalflow.global.requests.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.global.requests.entities.Request;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RequestFilterDto {

    private String search; // Search in title, description, requester name
    private Request.RequestStatus status;
    private int page = 0;
    private int size = 10;
    private String sortBy = "requestedDate";
    private String sortDirection = "DESC";
}
