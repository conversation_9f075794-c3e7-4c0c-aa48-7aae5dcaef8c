package tz.co.mikesanga.approvalflow.uaa.dtos;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MeDto {
    private String id;
    private String email;
    private String fullName;
    private String userName;
    private String phone;
    private String phoneCode;
    private String status;
    private List<Role> roles;
    private List<String> permissions;
}
