# User Management API

This document describes the complete user management API functionality that has been implemented following the existing architecture patterns.

> **📋 Related Documentation:**
> - [User Management Testing Guide](testing.md) - Comprehensive testing scenarios
> - [Implementation Details](../../implementation/README.md) - Technical implementation notes
> - [Security Model](../../architecture/security.md) - Authentication and authorization details

## Overview

The user management feature provides administrators with comprehensive functionality to manage users in the system, including creating, updating, deactivating, and assigning roles to users.

## Components Implemented

### 1. Updated UserAccount Entity
- Added `status` field (ACTIVE, INACTIVE)
- Added `lastLogin` field for tracking user activity
- Added validation annotations for data integrity
- Enhanced email and username constraints with uniqueness

### 2. DTOs Created
- `UserCreateRequestDto` - For creating and updating users (includes optional UUID field and role assignments)
- `UserResponseDto` - For user list responses with role information
- `UserFilterDto` - For filtering and pagination

### 3. Enhanced UserAccountRepository
- Added methods for filtering by status, role, and search terms
- Added pagination support using DataTablesRepository
- Added uniqueness validation methods
- Added complex queries for user management with proper JPA queries

### 4. UserManagementService
- Single `saveUser` method handles both create and update operations (follows RoleServiceImpl pattern)
- UUID presence determines operation type (null = create, present = update)
- Complete business logic for all user operations
- Password encryption for new users
- Role assignment logic with validation
- Transaction management for data consistency
- Comprehensive error handling

### 5. UserManagementController (GraphQL)
- All endpoints secured with `@PreAuthorize` annotations
- Proper GraphQL annotations following existing patterns
- Comprehensive CRUD operations
- Role assignment handled atomically within saveUser operation

### 6. Updated Permissions
Added the following permissions in `PermissionServiceImpl.seed()`:
- `ROLE_USERS_VIEW` - Can view users
- `ROLE_USERS_ADD` - Can add new users (includes role assignment)
- `ROLE_USERS_EDIT` - Can edit existing users (includes role assignment)
- `ROLE_USERS_DEACTIVATE` - Can deactivate/reactivate users

## GraphQL Endpoints

### Queries

#### List Users
```graphql
query {
  listUsers(filter: {
    search: "john"
    status: ACTIVE
    page: 0
    size: 10
    sortBy: "createdAt"
    sortDirection: "DESC"
  }) {
    status
    code
    dataList {
      uuid
      username
      fullName
      email
      status
      lastLogin
      createdAt
      roles {
        uuid
        name
        displayName
      }
    }
    extras
  }
}
```

#### Get User by UUID
```graphql
query {
  getUserByUuid(uuid: "user-uuid-here") {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
      lastLogin
      roles {
        uuid
        name
        displayName
      }
    }
  }
}
```

### Mutations

#### Create User (password optional - defaults to "ChangeMe123!")
```graphql
mutation {
  saveUser(userDto: {
    username: "newuser"
    # password: "custompassword123"  # Optional - if not provided, defaults to "ChangeMe123!"
    fullName: "New User"
    email: "<EMAIL>"
    phone: "1234567890"
    roleUuids: ["role-uuid-1", "role-uuid-2"]
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
    }
  }
}
```

#### Update User (including role assignment and optional password change)
```graphql
mutation {
  saveUser(userDto: {
    uuid: "user-uuid-here"
    username: "existinguser"
    # password: "newpassword123"  # Optional - only provide if changing password
    fullName: "Updated Name"
    email: "<EMAIL>"
    status: ACTIVE
    roleUuids: ["role-uuid-1", "role-uuid-3"]  # Roles are assigned atomically
  }) {
    status
    code
    data {
      uuid
      fullName
      email
      status
      roles {
        uuid
        name
        displayName
      }
    }
  }
}
```

#### Deactivate User
```graphql
mutation {
  deactivateUser(uuid: "user-uuid-here") {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
      lastLogin
    }
  }
}
```



## Security

All endpoints are secured with role-based access control:
- Only users with appropriate permissions can access each endpoint
- Password encryption using Spring Security's PasswordEncoder
- Input validation using Jakarta validation annotations
- Proper error handling without exposing sensitive information

## Validation

### User Creation
- Username: 3-50 characters, unique
- Password: Optional (defaults to "ChangeMe123!" if not provided)
- Email: Valid email format, unique
- Full name: Required, max 100 characters
- Phone: Optional, max 15 characters
- Roles: Optional, assigned via roleUuids array

### User Updates
- Email uniqueness validation (excluding current user)
- Username uniqueness validation (excluding current user)
- Password: Optional for updates (if provided, will be updated; if not provided, existing password preserved)
- All fields properly validated
- Status changes tracked
- Role assignments: Completely replaced with new roleUuids (atomic operation)

## Testing

Comprehensive test suite included:
- Unit tests for service layer
- Integration tests for complete workflows
- Test coverage for error scenarios
- H2 in-memory database for testing

## Usage Instructions

1. **Setup**: Ensure all permissions are seeded by running the application
2. **Access**: Use GraphQL endpoint at `/graphql` or GUI at `/gui`
3. **Authentication**: Obtain JWT token via login endpoint
4. **Authorization**: Ensure user has appropriate role permissions

## Error Handling

The system provides comprehensive error handling:
- Validation errors with field-level messages
- Business logic errors with meaningful descriptions
- Proper HTTP status codes
- Consistent error response format using `GqlResponseDto`

## Performance Considerations

- Pagination implemented for large user lists
- Efficient database queries with proper indexing
- Lazy loading for role relationships
- Transaction management for data consistency

## Future Enhancements

Potential improvements that could be added:
- Password reset functionality
- User profile picture upload
- Advanced filtering options
- User activity logging
- Bulk user operations
- Email notifications for user actions
