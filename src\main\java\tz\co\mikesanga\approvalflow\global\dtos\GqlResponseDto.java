package tz.co.mikesanga.approvalflow.global.dtos;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Getter
@Setter
@NoArgsConstructor
public class GqlResponseDto<T> {

    private Boolean status;

    private ResponseCode code;

    private T data;

    private List<T> dataList = new ArrayList<>();

    String errorDescription = null;

    private Map<String, String> fieldsErrors = new HashMap<>();

    private Map<String, Object> extras = new HashMap<>();

    public GqlResponseDto(Boolean status, ResponseCode code, T data, String errorDescription) {
        this.status = status;
        this.code = code;
        this.data = data;
        this.errorDescription = errorDescription;
    }


    public GqlResponseDto(Boolean status, ResponseCode code, T data) {
        this.status = status;
        this.code = code;
        this.data = data;
    }
    public GqlResponseDto(Boolean status, ResponseCode code, T data, Map<String,Object> extras) {
        this.status = status;
        this.code = code;
        this.data = data;
        this.extras = extras;
    }

    public GqlResponseDto(Boolean status, ResponseCode code, List<T> dataList) {
        this.status = status;
        this.code = code;
        this.dataList = dataList;
    }
    public GqlResponseDto(Boolean status, ResponseCode code, List<T> dataList, Map<String,Object> extras) {
        this.status = status;
        this.code = code;
        this.dataList = dataList;
        this.extras = extras;
    }
}
