package tz.co.mikesanga.approvalflow.uaa.controllers;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginRequest;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginResponse;
import tz.co.mikesanga.approvalflow.uaa.dtos.MeDto;
import tz.co.mikesanga.approvalflow.uaa.services.AuthService;

@RestController
public class AuthController {

    @Autowired
    private AuthService authService;


    @PostMapping("/auth/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        return authService.attemptLogin(loginRequest);
    }

    @GetMapping("/me")
    public ResponseEntity<MeDto> getMe(@AuthenticationPrincipal UserPrincipal userPrincipal){
        return authService.getMe(userPrincipal);
    }
}
