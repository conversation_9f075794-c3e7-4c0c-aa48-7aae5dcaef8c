package tz.co.mikesanga.approvalflow.uaa.services;


import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.time.LocalDateTime;
import java.util.Optional;

public interface UserAccountService {
    Optional<UserAccount> getOptionalByUsername(String username);

    Optional<UserAccount> getOptionalByEmail(String email);

    void seed();

    void updateLastLogin(String username, LocalDateTime loginTime);
}
