package tz.co.mikesanga.approvalflow.uaa.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface UserAccountRepository extends JpaRepository<UserAccount, Long>, DataTablesRepository<UserAccount, Long> {
    Optional<UserAccount> findFirstByUsername(String username);

    Optional<UserAccount> findFirstByEmail(String email);

    Optional<UserAccount> findFirstByUuid(UUID uuid);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);

    boolean existsByUsernameAndUuidNot(String username, UUID uuid);

    boolean existsByEmailAndUuidNot(String email, UUID uuid);

    @Query(value = "select r.name from Role r where exists (select 1 from UserAccount ua join ua.roles rl where ua.username =:username and rl.id = r.id)")
    List<String> getRoleByUsername(String username);

    @Query("SELECT ua FROM UserAccount ua WHERE " +
           "(:search IS NULL OR :search = '' OR " +
           "UPPER(ua.username) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(ua.fullName) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(ua.email) LIKE UPPER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR ua.status = :status)")
    Page<UserAccount> findUsersWithFilters(@Param("search") String search,
                                          @Param("status") UserAccount.UserStatus status,
                                          Pageable pageable);

    @Query("SELECT ua FROM UserAccount ua JOIN ua.roles r WHERE " +
           "r.uuid = :roleUuid AND " +
           "(:search IS NULL OR :search = '' OR " +
           "UPPER(ua.username) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(ua.fullName) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(ua.email) LIKE UPPER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR ua.status = :status)")
    Page<UserAccount> findUsersWithFiltersAndRole(@Param("search") String search,
                                                  @Param("status") UserAccount.UserStatus status,
                                                  @Param("roleUuid") UUID roleUuid,
                                                  Pageable pageable);

    @Query("SELECT COUNT(ua) FROM UserAccount ua WHERE ua.status = :status")
    long countByStatus(@Param("status") UserAccount.UserStatus status);
}
