# System Architecture

Comprehensive overview of the Approval Flow API system architecture, design patterns, and technical decisions.

## 🏗️ High-Level Architecture

### System Overview
The Approval Flow API is a Spring Boot-based GraphQL API system designed for managing approval workflows with user management, role-based access control, and request processing capabilities.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   GraphQL API   │    │   Database      │
│   Applications  │◄──►│   (Spring Boot) │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   JWT Auth      │
                       │   Security      │
                       └─────────────────┘
```

### Core Components
- **GraphQL API Layer** - Handles client requests and responses
- **Security Layer** - JWT authentication and role-based authorization
- **Business Logic Layer** - Services implementing business rules
- **Data Access Layer** - JPA repositories for database operations
- **Database Layer** - PostgreSQL for data persistence

## 🎯 Design Principles

### 1. Layered Architecture
```
┌─────────────────────────────────────────┐
│           Controllers Layer             │  ← GraphQL endpoints
├─────────────────────────────────────────┤
│            Services Layer               │  ← Business logic
├─────────────────────────────────────────┤
│          Repositories Layer             │  ← Data access
├─────────────────────────────────────────┤
│            Entities Layer               │  ← JPA entities
└─────────────────────────────────────────┘
```

### 2. Separation of Concerns
- **Controllers**: Handle HTTP/GraphQL requests, security, validation
- **Services**: Implement business logic, transaction management
- **Repositories**: Provide data access abstraction
- **Entities**: Represent domain objects and database structure

### 3. Dependency Injection
- Spring's IoC container manages component lifecycle
- Constructor injection for required dependencies
- Interface-based programming for loose coupling

## 🔧 Technology Stack

### Backend Framework
- **Spring Boot 3.4.5** - Application framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Data persistence and ORM
- **Hibernate** - JPA implementation

### GraphQL Implementation
- **GraphQL SPQR** - Schema-first GraphQL for Java
- **io.leangen.graphql** - Annotation-based GraphQL integration

### Database
- **PostgreSQL** - Primary database
- **H2** - In-memory database for testing
- **Flyway/Liquibase** - Database migration (future)

### Security
- **JWT (JSON Web Tokens)** - Stateless authentication
- **BCrypt** - Password hashing
- **Role-based Access Control** - Permission system

### Build and Deployment
- **Gradle** - Build automation
- **Docker** - Containerization
- **Java 17** - Runtime environment

## 📊 Data Architecture

### Entity Relationship Overview
```
UserAccount ──┐
              ├── Many-to-Many ──► Role ──► Many-to-Many ──► Permission
              │
              └── One-to-Many ──► Request
                                    │
                                    └── Many-to-One ──► UserAccount (approver)
```

### Core Entities

#### UserAccount
- Primary user entity
- Stores authentication and profile information
- Links to roles for permission management
- Tracks user status and activity

#### Role
- Groups permissions into logical roles
- Many-to-many relationship with users and permissions
- Hierarchical permission structure

#### Permission
- Atomic permission units
- Named using `ROLE_ENTITY_ACTION` convention
- Granular access control

#### Request
- Approval workflow entity
- Links to requesting user and approving user
- Status-based workflow (PENDING → APPROVED/REJECTED)

### Database Schema Patterns

#### Base Entity Pattern
```java
@MappedSuperclass
public abstract class UaaBaseEntity<T> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private T id;
    
    @Column(unique = true, nullable = false)
    private UUID uuid;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    private LocalDateTime deletedAt; // Soft delete
}
```

#### Audit Trail
- All entities include creation and modification timestamps
- Soft delete capability with `deletedAt` field
- UUID-based external identifiers for security

## 🔒 Security Architecture

### Authentication Flow
```
1. Client sends credentials to /auth/login
2. Server validates credentials
3. Server generates JWT token
4. Client includes token in Authorization header
5. Server validates token on each request
6. Server checks user permissions for endpoint access
```

### Authorization Model

#### Role-Based Access Control (RBAC)
- Users are assigned roles
- Roles contain permissions
- Permissions control access to specific operations
- Hierarchical permission inheritance

#### Permission Naming Convention
```
ROLE_ENTITY_ACTION
Examples:
- ROLE_USERS_VIEW
- ROLE_USERS_ADD
- ROLE_REQUESTS_CREATE
- ROLE_REQUESTS_APPROVE
```

#### Security Layers
1. **Network Security** - HTTPS, CORS configuration
2. **Authentication** - JWT token validation
3. **Authorization** - Role-based permission checks
4. **Data Access** - Ownership validation in services
5. **Input Validation** - DTO validation and sanitization

## 🔄 Request Processing Flow

### GraphQL Request Lifecycle
```
1. HTTP Request → Spring Security Filter Chain
2. JWT Token Validation
3. User Authentication & Role Loading
4. GraphQL Schema Validation
5. @PreAuthorize Permission Check
6. Controller Method Execution
7. Service Layer Business Logic
8. Repository Data Access
9. Response Serialization
10. HTTP Response
```

### Transaction Management
- Service layer methods are transactional
- Automatic rollback on exceptions
- Optimistic locking for concurrent access
- Connection pooling for performance

## 📈 Performance Architecture

### Caching Strategy
- **Entity Caching** - Second-level Hibernate cache
- **Query Caching** - Frequently accessed data
- **Session Management** - Stateless JWT tokens

### Database Optimization
- **Indexing** - Strategic indexes on query columns
- **Lazy Loading** - Efficient relationship loading
- **Pagination** - Large dataset handling
- **Connection Pooling** - Database connection management

### Scalability Considerations
- **Stateless Design** - Horizontal scaling capability
- **Database Separation** - Read/write splitting potential
- **Microservice Ready** - Modular architecture
- **Container Support** - Docker deployment

## 🧪 Testing Architecture

### Test Pyramid
```
┌─────────────────┐
│   E2E Tests     │  ← Full system integration
├─────────────────┤
│ Integration     │  ← Controller + Service + Repository
│ Tests           │
├─────────────────┤
│   Unit Tests    │  ← Individual component testing
└─────────────────┘
```

### Test Configuration
- **H2 Database** - In-memory testing
- **Test Profiles** - Isolated test configuration
- **Mock Objects** - External dependency mocking
- **Test Containers** - Integration testing with real databases

## 🔧 Configuration Architecture

### Environment Management
- **Profiles** - Environment-specific configurations
- **External Configuration** - Environment variables
- **Property Hierarchy** - Override capabilities
- **Validation** - Configuration validation on startup

### Configuration Layers
1. **Default Properties** - Built-in defaults
2. **Profile Properties** - Environment-specific
3. **Environment Variables** - Runtime configuration
4. **Command Line Arguments** - Deployment overrides

## 📚 Integration Architecture

### API Design
- **GraphQL Schema** - Type-safe API contracts
- **Consistent Responses** - Standardized response format
- **Error Handling** - Structured error responses
- **Versioning Strategy** - Schema evolution support

### External Integration Points
- **Database** - PostgreSQL via JDBC
- **Authentication** - JWT token validation
- **Logging** - Structured logging with correlation IDs
- **Monitoring** - Health checks and metrics

## 🔮 Future Architecture Considerations

### Scalability Enhancements
- **Microservice Decomposition** - Service separation
- **Event-Driven Architecture** - Asynchronous processing
- **CQRS Pattern** - Command/Query separation
- **API Gateway** - Centralized routing and security

### Technology Evolution
- **GraphQL Federation** - Schema composition
- **Reactive Programming** - Non-blocking I/O
- **Cloud Native** - Kubernetes deployment
- **Observability** - Distributed tracing

## 📖 Related Documentation

- [Security Model](security.md) - Detailed security implementation
- [Database Schema](database-schema.md) - Complete schema documentation
- [GraphQL Schema](graphql-schema.md) - API schema reference
- [Development Patterns](../development/patterns.md) - Implementation patterns

---

*This architecture supports current requirements while providing flexibility for future growth and evolution.*
