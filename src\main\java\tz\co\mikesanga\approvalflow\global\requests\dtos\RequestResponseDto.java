package tz.co.mikesanga.approvalflow.global.requests.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.global.requests.entities.Request;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RequestResponseDto {

    private String uuid;
    private String title;
    private String description;
    private Request.RequestStatus status;
    private LocalDateTime requestedDate;
    private LocalDateTime approvedDate;
    private String approvalComment;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Nested DTOs for user information
    private UserInfo requestedBy;
    private UserInfo approvedBy;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserInfo {
        private String uuid;
        private String username;
        private String fullName;
        private String email;
    }
}
