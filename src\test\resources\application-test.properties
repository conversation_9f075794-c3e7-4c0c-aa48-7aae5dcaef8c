# Test configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# JWT configuration for tests
security.jwt.secret-key=test-secret-key-for-testing-purposes-only

# GraphQL configuration
graphql.spqr.gui.enabled=false

# Logging
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN
logging.level.io.leangen.graphql=WARN
logging.level.org.hibernate.SQL=DEBUG
