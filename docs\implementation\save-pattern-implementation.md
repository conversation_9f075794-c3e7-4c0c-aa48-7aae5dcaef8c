# UserManagement Service - Save Pattern Implementation

This document describes the implementation of the unified save pattern for UserManagement service, following the same approach as RoleService.save() and RequestService.saveRequest().

## Overview

The UserManagement service has been updated to follow the established codebase pattern where a single `saveUser` method handles both create and update operations based on the presence of a UUID in the DTO.

## Changes Made

### 1. UserCreateRequestDto Updated
**File**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/dtos/UserCreateRequestDto.java`

**Changes**:
- ✅ Added optional `uuid` field
- ✅ Added `status` field for user status updates
- ✅ Made `password` validation conditional (required only for create operations)
- ✅ Same DTO now handles both create and update operations

**Key Fields**:
```java
private String uuid;              // Optional - determines create vs update
private String username;          // Required
private String password;          // Required only for create (when uuid is null)
private String fullName;          // Required
private String email;             // Required, unique
private String phoneCode;         // Optional
private String phone;             // Optional
private UserAccount.UserStatus status; // Optional for updates
private List<String> roleUuids;   // Optional role assignments
```

### 2. UserManagementService Interface
**File**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/services/UserManagementService.java`

**Changes**:
- ✅ Removed `createUser()` method
- ✅ Removed `updateUser()` method
- ✅ Added single `saveUser()` method

### 3. UserManagementServiceImpl
**File**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/services/UserManagementServiceImpl.java`

**Changes**:
- ✅ Implemented `saveUser()` method following RoleServiceImpl pattern
- ✅ Added `getOptionalByUuid()` helper method
- ✅ Removed old `createUser()` and `updateUser()` methods

**Business Logic**:
```java
// Check if this is an update operation (UUID provided)
Optional<UserAccount> optionalUser = getOptionalByUuid(userDto.getUuid());
if (userDto.getUuid() != null && optionalUser.isEmpty()) {
    return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, 
        "User edit failed: The user was not found or may have been removed.");
}

UserAccount userAccount = optionalUser.orElse(new UserAccount());

if (userDto.getUuid() != null) {
    // Update operation logic
} else {
    // Create operation logic
}
```

### 4. UserManagementController
**File**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/controllers/UserManagementController.java`

**Changes**:
- ✅ Removed `createUser` mutation
- ✅ Removed `updateUser` mutation
- ✅ Added single `saveUser` mutation
- ✅ Updated permissions to allow both `ROLE_USERS_ADD` and `ROLE_USERS_EDIT`

### 5. Cleanup
- ✅ Removed `UserUpdateRequestDto.java` (no longer needed)
- ✅ Updated documentation to reflect new pattern

## Business Rules Maintained

### Create Operation (when uuid is null/empty):
- ✅ **Password required**: Must provide password for new users
- ✅ **Username uniqueness**: Validates username doesn't exist
- ✅ **Email uniqueness**: Validates email doesn't exist
- ✅ **Default status**: Sets status to ACTIVE
- ✅ **Password encoding**: Encrypts password using PasswordEncoder
- ✅ **Role assignment**: Assigns roles if provided

### Update Operation (when uuid is provided):
- ✅ **User existence**: Validates user exists
- ✅ **Username uniqueness**: Validates username uniqueness excluding current user
- ✅ **Email uniqueness**: Validates email uniqueness excluding current user
- ✅ **Password optional**: Password not required for updates
- ✅ **Status updates**: Can update user status
- ✅ **Role updates**: Can update role assignments
- ✅ **Timestamp tracking**: Updates `updatedAt` timestamp

## Security & Validation

### Permissions:
- `@PreAuthorize("hasAnyRole('ROLE_USERS_ADD', 'ROLE_USERS_EDIT')")`
- Users with either permission can use the saveUser endpoint

### Validation:
- **Username**: 3-50 characters, unique
- **Password**: Minimum 6 characters (required only for create)
- **Email**: Valid email format, unique
- **Full name**: Required, max 100 characters
- **Phone**: Optional, max 15 characters

### Error Handling:
- Consistent error responses using `GqlResponseDto`
- Proper error codes (DUPLICATE, INVALID_REQUEST, etc.)
- Detailed error messages for validation failures

## API Usage Examples

### Create New User:
```graphql
mutation {
  saveUser(userDto: {
    username: "newuser"
    password: "securepassword123"
    fullName: "New User"
    email: "<EMAIL>"
    phone: "1234567890"
    roleUuids: ["role-uuid-1", "role-uuid-2"]
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
    }
  }
}
```

### Update Existing User:
```graphql
mutation {
  saveUser(userDto: {
    uuid: "existing-user-uuid"
    username: "updateduser"
    fullName: "Updated Name"
    email: "<EMAIL>"
    status: ACTIVE
    roleUuids: ["role-uuid-1", "role-uuid-3"]
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
    }
  }
}
```

## Pattern Consistency

This implementation now perfectly matches the patterns used in:
- ✅ **RoleServiceImpl.save()** - Same UUID-based create/update logic
- ✅ **RequestServiceImpl.saveRequest()** - Same helper method pattern
- ✅ **Consistent error handling** - Same response codes and messages
- ✅ **Same validation approach** - Conditional validation based on operation type

## Benefits

1. **Simplified API**: Single endpoint for both operations
2. **Consistent Architecture**: Follows established codebase patterns
3. **Reduced Complexity**: Less code duplication
4. **Better Maintainability**: Single method to maintain
5. **Unified Validation**: Consistent validation logic
6. **Pattern Compliance**: Matches existing service implementations

The UserManagement service now follows the exact same pattern as the rest of the codebase! 🎉
