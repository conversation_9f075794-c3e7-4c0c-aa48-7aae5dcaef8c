# API Documentation

This section contains comprehensive documentation for all GraphQL API endpoints in the Approval Flow system.

## 🔗 API Endpoints

### Core Modules

#### 👥 [User Management](user-management/README.md)
Complete user lifecycle management including creation, updates, role assignments, and status management.

**Key Features:**
- User creation with optional password (defaults to "ChangeMe123!")
- User updates and status management
- Role assignment and management
- User filtering and pagination

**Endpoints:**
- `saveUser` - Create or update users
- `listUsers` - Get paginated user list with filters
- `getUserByUuid` - Get single user details
- `deactivateUser` - Deactivate user account

#### 📋 [Request Management](request-management/README.md)
Approval request system allowing users to create requests and administrators to approve or reject them.

**Key Features:**
- Request creation and updates
- Approval workflow (PENDING → APPROVED/REJECTED)
- User-specific and admin views
- Request filtering and search

**Endpoints:**
- `saveRequest` - Create or update requests
- `getMyRequests` - Get user's own requests
- `getAllRequests` - Get all requests (admin only)
- `approveRequest` - Approve or reject requests
- `deleteRequest` - Delete pending requests

#### 🔐 [Authentication](authentication/README.md)
JWT-based authentication system with role-based access control.

**Key Features:**
- JWT token authentication
- Role-based permissions
- Secure login/logout
- Token validation

#### 🎭 [Role Management](role-management/README.md)
Role and permission management system.

**Key Features:**
- Role creation and management
- Permission assignment
- Role-based access control

## 🔒 Security Model

All API endpoints are secured using JWT tokens and role-based permissions:

### Permission Structure
- `ROLE_ENTITY_ACTION` format (e.g., `ROLE_USERS_VIEW`)
- Hierarchical permission system
- Least privilege principle

### Common Permissions
- **Users**: `ROLE_USERS_VIEW`, `ROLE_USERS_ADD`, `ROLE_USERS_EDIT`, `ROLE_USERS_DEACTIVATE`
- **Requests**: `ROLE_REQUESTS_CREATE`, `ROLE_REQUESTS_VIEW`, `ROLE_REQUESTS_VIEW_ALL`, `ROLE_REQUESTS_APPROVE`, `ROLE_REQUESTS_EDIT`, `ROLE_REQUESTS_DELETE`

## 📊 Response Format

All API responses follow a consistent format using `GqlResponseDto`:

```json
{
  "status": true,
  "code": "SUCCESS",
  "data": { /* response data */ },
  "dataList": [ /* for list responses */ ],
  "extras": { /* pagination info */ },
  "errorDescription": null,
  "fieldsErrors": null
}
```

### Response Codes
- `SUCCESS` - Operation completed successfully
- `DUPLICATE` - Duplicate entry (e.g., username already exists)
- `NO_RECORD_FOUND` - Requested resource not found
- `INVALID_REQUEST` - Invalid input or business rule violation
- `CUSTOM` - Custom error with specific message

## 🚀 Getting Started

1. **Authentication**: Obtain JWT token via login endpoint
2. **Authorization**: Ensure your user has required permissions
3. **GraphQL**: Use GraphQL endpoint at `/graphql` or GUI at `/gui`
4. **Headers**: Include `Authorization: Bearer <token>` in requests

## 📝 Common Patterns

### Pagination
Most list endpoints support pagination:
```graphql
{
  filter: {
    page: 0,
    size: 10,
    sortBy: "createdAt",
    sortDirection: "DESC"
  }
}
```

### Filtering
Search and filter capabilities:
```graphql
{
  filter: {
    search: "search term",
    status: ACTIVE,
    page: 0,
    size: 10
  }
}
```

### Create vs Update
Many endpoints use UUID presence to determine operation:
- **Create**: Omit `uuid` field or set to `null`
- **Update**: Include existing `uuid` field

## 🧪 Testing

See the [Testing Documentation](../testing/README.md) for comprehensive testing guides and examples for each API module.

## 📚 Additional Resources

- [Architecture Overview](../architecture/README.md)
- [Security Model](../architecture/security.md)
- [Development Patterns](../development/patterns.md)
- [Troubleshooting](../implementation/troubleshooting.md)
