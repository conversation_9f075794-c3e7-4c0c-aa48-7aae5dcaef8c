package tz.co.mikesanga.approvalflow.uaa.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserFilterDto {

    private String search; // Search in username, fullName, email
    private UserAccount.UserStatus status;
    private String roleUuid;
    private int page = 0;
    private int size = 10;
    private String sortBy = "createdAt";
    private String sortDirection = "DESC";
}
