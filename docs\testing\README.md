# Testing Documentation

Comprehensive testing guide for the Approval Flow GraphQL API system.

## 🧪 Testing Overview

This section provides detailed testing documentation for all API endpoints, including setup instructions, test scenarios, and validation examples.

## 📋 Testing Categories

### 🔗 API Testing
Complete testing guides for each API module:

#### 👥 User Management Testing
- **Location**: [User Management Testing](../api/user-management/testing.md)
- **Coverage**: User creation, updates, role assignments, deactivation
- **Scenarios**: Success cases, validation errors, permission checks
- **Prerequisites**: Admin JWT token, GraphQL access

#### 📋 Request Management Testing  
- **Location**: [Request Management Testing](../api/request-management/testing.md)
- **Coverage**: Request lifecycle, approval workflow, user permissions
- **Scenarios**: Create, update, approve, reject, delete operations
- **Prerequisites**: User and admin JWT tokens

### 🔒 Security Testing
- **Authentication**: JWT token validation
- **Authorization**: Role-based permission checks
- **Data Isolation**: User can only access own data
- **Input Validation**: Malicious input handling

### ⚡ Performance Testing
- **Pagination**: Large dataset handling
- **Search**: Query performance with filters
- **Concurrent Access**: Multiple user scenarios
- **Load Testing**: System under stress

## 🚀 Quick Start Testing

### Prerequisites
1. **Running Application**: Ensure the application is running
2. **Database**: Have test data or clean database
3. **GraphQL Access**: Access to `/graphql` endpoint or `/gui`
4. **Authentication**: Valid JWT tokens for different user roles

### Basic Test Flow
1. **Authenticate**: Get JWT token via login
2. **Set Headers**: Include `Authorization: Bearer <token>`
3. **Execute Queries**: Run GraphQL mutations/queries
4. **Validate Responses**: Check status, data, and error handling

## 🛠️ Testing Tools

### GraphQL GUI
- **URL**: `http://localhost:8082/gui`
- **Features**: Interactive query builder, schema explorer
- **Usage**: Manual testing and exploration

### cURL Examples
```bash
# Login to get token
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'

# GraphQL Query with token
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{"query": "query { listUsers { status code dataList { uuid username } } }"}'
```

### Postman Collection
- Import GraphQL schema
- Set up environment variables for tokens
- Create test collections for each module

## 📊 Test Data Management

### Test Users
Create test users with different roles:
```graphql
# Admin user (has all permissions)
# Regular user (limited permissions)
# Inactive user (for status testing)
```

### Test Requests
Create requests in different states:
```graphql
# PENDING requests (for approval testing)
# APPROVED requests (for read-only testing)
# REJECTED requests (for status validation)
```

## ✅ Validation Checklist

### Response Validation
- [ ] `status` field is boolean
- [ ] `code` field matches expected response code
- [ ] `data` or `dataList` contains expected structure
- [ ] `extras` contains pagination info (for list responses)
- [ ] `errorDescription` is null for successful operations
- [ ] `fieldsErrors` contains validation errors when applicable

### Security Validation
- [ ] Unauthorized requests return proper errors
- [ ] Users can only access their own data
- [ ] Admin permissions work correctly
- [ ] Invalid tokens are rejected
- [ ] Sensitive data is not exposed

### Business Logic Validation
- [ ] Create operations set proper defaults
- [ ] Update operations preserve existing data
- [ ] Status transitions follow business rules
- [ ] Validation rules are enforced
- [ ] Timestamps are updated correctly

## 🐛 Error Testing

### Common Error Scenarios
1. **Authentication Errors**
   - Missing token
   - Invalid token
   - Expired token

2. **Authorization Errors**
   - Insufficient permissions
   - Accessing other user's data

3. **Validation Errors**
   - Required fields missing
   - Invalid field formats
   - Business rule violations

4. **Not Found Errors**
   - Non-existent UUIDs
   - Deleted resources

### Error Response Format
```json
{
  "status": false,
  "code": "ERROR_CODE",
  "data": null,
  "errorDescription": "Human readable error message",
  "fieldsErrors": {
    "fieldName": ["Validation error message"]
  }
}
```

## 📈 Performance Testing

### Load Testing Scenarios
- **Concurrent Users**: Multiple users accessing system simultaneously
- **Large Datasets**: Testing with thousands of records
- **Complex Queries**: Nested GraphQL queries with multiple joins
- **Pagination**: Large result sets with pagination

### Performance Metrics
- **Response Time**: < 500ms for simple queries
- **Throughput**: Requests per second
- **Memory Usage**: Monitor during load tests
- **Database Performance**: Query execution times

## 🔄 Continuous Testing

### Automated Testing
- Unit tests for service layer
- Integration tests for complete workflows
- API tests for endpoint validation
- Performance regression tests

### Test Environment
- Separate test database
- Test data fixtures
- Automated test execution
- Test result reporting

## 📚 Additional Resources

- [API Documentation](../api/README.md)
- [Development Patterns](../development/patterns.md)
- [Troubleshooting Guide](../implementation/troubleshooting.md)
- [Security Architecture](../architecture/security.md)

---

*For specific testing examples, see the individual module testing guides linked above.*
