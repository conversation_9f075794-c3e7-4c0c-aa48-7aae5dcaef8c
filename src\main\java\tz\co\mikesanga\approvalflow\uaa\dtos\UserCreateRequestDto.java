package tz.co.mikesanga.approvalflow.uaa.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserCreateRequestDto {

    // Optional UUID - if present, this is an update operation; if null/empty, this is a create operation
    private String uuid;

    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    private String username;

    // Password is optional - if not provided, a default password will be set
    @Size(min = 6, message = "Password must be at least 6 characters")
    private String password;

    @NotBlank(message = "Full name is required")
    @Size(max = 100, message = "Full name cannot exceed 100 characters")
    private String fullName;

    @Email(message = "Email should be valid")
    @NotBlank(message = "Email is required")
    private String email;

    @Size(max = 5, message = "Phone code cannot exceed 5 characters")
    private String phoneCode;

    @Size(max = 15, message = "Phone number cannot exceed 15 characters")
    private String phone;

    private UserAccount.UserStatus status;

    private List<String> roleUuids;
}
