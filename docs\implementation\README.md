# Implementation Documentation

Detailed implementation notes, patterns, and technical decisions for the Approval Flow API system.

## 📋 Implementation Notes

This section contains comprehensive documentation about specific implementation details, design decisions, and technical patterns used throughout the system.

## 🔧 Feature Implementation Details

### User Management Implementation
- **[Default Password Implementation](default-password-implementation.md)** - How default passwords work for admin-created users
- **[Role Assignment Consolidation](role-assignment-consolidation.md)** - Unified role assignment within saveUser method
- **[Save Pattern Implementation](save-pattern-implementation.md)** - Unified create/update pattern following codebase standards

### Request Management Implementation
- **[Request Workflow](request-workflow.md)** - Complete request lifecycle implementation
- **[Security Model](request-security.md)** - Permission-based access control for requests
- **[Status Management](request-status.md)** - Request status transitions and business rules

## 🏗️ Architecture Patterns

### Established Patterns
The codebase follows consistent patterns across all modules:

#### 1. **Layered Architecture**
```
Controllers → Services → Repositories → Entities
```
- **Controllers**: Handle GraphQL requests/responses, security
- **Services**: Business logic, transaction management
- **Repositories**: Data access, queries
- **Entities**: JPA entities extending UaaBaseEntity

#### 2. **Unified Save Pattern**
Single method handles both create and update operations:
```java
// UUID presence determines operation type
if (dto.getUuid() != null) {
    // Update existing entity
} else {
    // Create new entity
}
```

#### 3. **Permission Naming Convention**
```
ROLE_ENTITY_ACTION
Examples:
- ROLE_USERS_VIEW
- ROLE_REQUESTS_CREATE
- ROLE_REQUESTS_APPROVE
```

#### 4. **Response Format**
Consistent response structure using `GqlResponseDto`:
```java
return new GqlResponseDto<>(
    true,                    // status
    ResponseCode.SUCCESS,    // code
    responseData,           // data
    null                    // errorDescription
);
```

### GraphQL Implementation
- **Annotations**: `@GraphQLApi`, `@GraphQLQuery`, `@GraphQLMutation`
- **Security**: `@PreAuthorize` on all endpoints
- **Library**: `io.leangen.graphql` for GraphQL integration

### Entity Design
- **Base Entity**: All entities extend `UaaBaseEntity<Long>`
- **Relationships**: Proper JPA annotations with correct fetch types
- **Validation**: Jakarta validation annotations
- **JSON Handling**: `@JsonIgnore` for sensitive fields

## 🔒 Security Implementation

### JWT Authentication
- Token-based authentication
- Role-based authorization
- Permission checking at endpoint level

### Data Access Control
- Users can only access their own data
- Admin users have broader access
- Ownership validation in service layer

### Input Validation
- DTO validation using Jakarta annotations
- Business rule validation in services
- SQL injection prevention through JPA

## 📊 Database Design

### Entity Relationships
- **UserAccount** ↔ **Role** (Many-to-Many)
- **Role** ↔ **Permission** (Many-to-Many)
- **Request** → **UserAccount** (Many-to-One for requestedBy/approvedBy)

### Naming Conventions
- Table names: snake_case
- Column names: snake_case
- Entity names: PascalCase
- Field names: camelCase

## 🔄 Transaction Management

### Service Layer Transactions
- `@Transactional` on service methods
- Proper rollback on exceptions
- Atomic operations for complex workflows

### Data Consistency
- Validation before persistence
- Referential integrity enforcement
- Optimistic locking where needed

## 📝 Code Quality Standards

### Naming Conventions
- **Classes**: PascalCase (e.g., `UserManagementService`)
- **Methods**: camelCase (e.g., `saveUser`)
- **Variables**: camelCase (e.g., `userAccount`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_PASSWORD`)

### Documentation Standards
- JavaDoc for public methods
- Inline comments for complex logic
- README files for major features
- API documentation with examples

### Error Handling
- Consistent error response format
- Meaningful error messages
- Proper exception handling
- Logging for debugging

## 🧪 Testing Implementation

### Test Structure
- Unit tests for services
- Integration tests for controllers
- Repository tests with H2 database
- Mock objects for external dependencies

### Test Data Management
- Test fixtures for consistent data
- Database cleanup between tests
- Separate test configuration

## 📈 Performance Considerations

### Database Optimization
- Proper indexing on frequently queried fields
- Lazy loading for relationships
- Pagination for large result sets
- Query optimization

### Caching Strategy
- Entity-level caching where appropriate
- Query result caching for static data
- Session management

## 🔧 Development Workflow

### Code Organization
- Package structure by feature
- Separation of concerns
- Dependency injection
- Configuration externalization

### Build and Deployment
- Gradle build system
- Spring Boot packaging
- Environment-specific configurations
- Database migration scripts

## 📚 Migration Guides

### Version Migrations
- **[Migration Guides](migrations/README.md)** - Version-specific migration instructions
- Database schema changes
- API breaking changes
- Configuration updates

## 🐛 Troubleshooting

### Common Issues
- **[Troubleshooting Guide](troubleshooting.md)** - Common problems and solutions
- Database connection issues
- Authentication problems
- Permission errors
- Performance issues

## 📖 Additional Resources

- [API Documentation](../api/README.md)
- [Architecture Overview](../architecture/README.md)
- [Development Guide](../development/README.md)
- [Testing Documentation](../testing/README.md)

---

*This documentation is maintained alongside code changes to ensure accuracy and relevance.*
