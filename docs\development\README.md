# Development Guide

Complete guide for developers working on the Approval Flow API system.

## 🚀 Getting Started

### Prerequisites
- **Java 17+** - Required for Spring Boot 3.x
- **PostgreSQL** - Primary database
- **Gradle** - Build system
- **IDE** - IntelliJ IDEA recommended

### Development Setup
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd approval-flow-be
   ```

2. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb approval_flow_dev
   ```

3. **Configuration**
   ```bash
   # Copy and configure application properties
   cp src/main/resources/application.properties.example src/main/resources/application.properties
   ```

4. **Build and Run**
   ```bash
   ./gradlew bootRun
   ```

5. **Verify Setup**
   - API: `http://localhost:8082/graphql`
   - GraphQL GUI: `http://localhost:8082/gui`

## 🏗️ Architecture Overview

### Project Structure
```
src/main/java/tz/co/mikesanga/approvalflow/
├── uaa/
│   ├── controllers/     # GraphQL controllers
│   ├── services/        # Business logic
│   ├── repositories/    # Data access
│   ├── entities/        # JPA entities
│   ├── dtos/           # Data transfer objects
│   ├── config/         # Configuration classes
│   └── security/       # Security components
└── ApprovalFlowApplication.java
```

### Technology Stack
- **Spring Boot 3.4.5** - Application framework
- **Spring Security** - Authentication/Authorization
- **Spring Data JPA** - Data persistence
- **GraphQL SPQR** - GraphQL implementation
- **PostgreSQL** - Database
- **JWT** - Token-based authentication
- **Lombok** - Code generation

## 📋 Development Patterns

### 1. Layered Architecture
Follow the established pattern:
```java
@GraphQLApi
@Service
public class EntityController {
    // GraphQL endpoints with @PreAuthorize
}

@Service
@Transactional
public class EntityServiceImpl implements EntityService {
    // Business logic and validation
}

@Repository
public interface EntityRepository extends JpaRepository<Entity, Long> {
    // Data access methods
}
```

### 2. Unified Save Pattern
Use single method for create/update operations:
```java
public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
    Optional<Entity> optional = getOptionalByUuid(dto.getUuid());
    
    if (dto.getUuid() != null && optional.isEmpty()) {
        return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, 
            "Entity not found");
    }
    
    Entity entity = optional.orElse(new Entity());
    
    if (dto.getUuid() != null) {
        // Update logic
    } else {
        // Create logic
    }
    
    // Common save logic
}
```

### 3. Permission Naming
Follow the established convention:
```java
// Format: ROLE_ENTITY_ACTION
ROLE_USERS_VIEW
ROLE_USERS_ADD
ROLE_USERS_EDIT
ROLE_REQUESTS_CREATE
ROLE_REQUESTS_APPROVE
```

### 4. Response Format
Use consistent response structure:
```java
// Success response
return new GqlResponseDto<>(true, ResponseCode.SUCCESS, data, null);

// Error response
return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Error message");

// Validation error
return new GqlResponseDto<>(false, ResponseCode.INVALID_REQUEST, null, 
    "Validation failed", validationErrors);
```

## 🔧 Adding New Features

### 1. Create Entity
```java
@Entity
@Table(name = "entity_name")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityName extends UaaBaseEntity<Long> {
    
    @Column(nullable = false)
    private String requiredField;
    
    @Column
    private String optionalField;
    
    // Relationships with proper fetch types
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private UserAccount user;
}
```

### 2. Create DTOs
```java
// Request DTO
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityCreateDto {
    private String uuid; // Optional for create/update pattern
    
    @NotBlank(message = "Field is required")
    private String requiredField;
    
    private String optionalField;
}

// Response DTO
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityResponseDto {
    private String uuid;
    private String requiredField;
    private String optionalField;
    private LocalDateTime createdAt;
}
```

### 3. Create Repository
```java
@Repository
public interface EntityRepository extends JpaRepository<EntityName, Long>, 
                                         DataTablesRepository<EntityName, Long> {
    
    Optional<EntityName> findFirstByUuid(UUID uuid);
    
    @Query("SELECT e FROM EntityName e WHERE ...")
    Page<EntityName> findWithFilters(@Param("param") String param, Pageable pageable);
}
```

### 4. Create Service
```java
@Service
@Transactional
@Slf4j
public class EntityServiceImpl implements EntityService {
    
    @Autowired
    private EntityRepository repository;
    
    public GqlResponseDto<EntityResponseDto> saveEntity(EntityCreateDto dto) {
        // Follow unified save pattern
    }
    
    private Optional<EntityName> getOptionalByUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return Optional.empty();
        }
        return repository.findFirstByUuid(UUID.fromString(uuid));
    }
}
```

### 5. Create Controller
```java
@GraphQLApi
@Service
@Slf4j
public class EntityController {
    
    @Autowired
    private EntityService entityService;
    
    @GraphQLMutation
    @PreAuthorize("hasAnyRole('ROLE_ENTITY_ADD', 'ROLE_ENTITY_EDIT')")
    public GqlResponseDto<EntityResponseDto> saveEntity(EntityCreateDto dto) {
        return entityService.saveEntity(dto);
    }
    
    @GraphQLQuery
    @PreAuthorize("hasRole('ROLE_ENTITY_VIEW')")
    public GqlResponseDto<List<EntityResponseDto>> listEntities(FilterDto filter) {
        return entityService.listEntities(filter);
    }
}
```

### 6. Add Permissions
Update `PermissionServiceImpl.seed()`:
```java
// Entity Management Permissions
permissions.add(new Permission("ROLE_ENTITY_VIEW", "Can view entities"));
permissions.add(new Permission("ROLE_ENTITY_ADD", "Can add entities"));
permissions.add(new Permission("ROLE_ENTITY_EDIT", "Can edit entities"));
permissions.add(new Permission("ROLE_ENTITY_DELETE", "Can delete entities"));
```

## 🧪 Testing

### Unit Tests
```java
@ExtendWith(MockitoExtension.class)
class EntityServiceImplTest {
    
    @Mock
    private EntityRepository repository;
    
    @InjectMocks
    private EntityServiceImpl service;
    
    @Test
    void saveEntity_CreateNew_Success() {
        // Test create operation
    }
    
    @Test
    void saveEntity_UpdateExisting_Success() {
        // Test update operation
    }
}
```

### Integration Tests
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class EntityControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void saveEntity_WithValidData_ReturnsSuccess() {
        // Test complete workflow
    }
}
```

## 🔒 Security Guidelines

### Authentication
- All endpoints require JWT token
- Use `@PreAuthorize` on all GraphQL endpoints
- Validate user permissions in service layer

### Data Access
- Users can only access their own data
- Admin users have broader access
- Implement ownership checks in services

### Input Validation
- Use Jakarta validation annotations
- Validate business rules in services
- Sanitize user input

## 📊 Database Guidelines

### Migrations
- Use Flyway or Liquibase for schema changes
- Version all database changes
- Test migrations on development data

### Performance
- Add indexes on frequently queried columns
- Use appropriate fetch types for relationships
- Implement pagination for large datasets

## 🔧 Configuration

### Environment Variables
```properties
# Database
spring.datasource.url=**************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# JWT
jwt.secret=your-secret-key
jwt.expiration=86400000

# GraphQL
graphql.servlet.mapping=/graphql
graphql.servlet.enabled=true
```

### Profiles
- `dev` - Development environment
- `test` - Testing environment
- `prod` - Production environment

## 📝 Code Quality

### Code Style
- Follow Java naming conventions
- Use meaningful variable names
- Keep methods small and focused
- Add JavaDoc for public methods

### Git Workflow
- Feature branches for new development
- Pull requests for code review
- Commit messages following conventional format
- Regular rebasing to keep history clean

## 📚 Resources

### Documentation
- [API Documentation](../api/README.md)
- [Testing Guide](../testing/README.md)
- [Implementation Notes](../implementation/README.md)

### External Resources
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [GraphQL SPQR](https://github.com/leangen/graphql-spqr)
- [Spring Security](https://spring.io/projects/spring-security)

---

*Happy coding! 🚀*
