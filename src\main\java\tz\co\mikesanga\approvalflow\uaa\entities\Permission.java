package tz.co.mikesanga.approvalflow.uaa.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "permissions")
public class Permission  extends UaaBaseEntity<Long> {

    @Column(columnDefinition = "varchar(30)")
    private String name;

    private String description;
    private String groupName;
}
