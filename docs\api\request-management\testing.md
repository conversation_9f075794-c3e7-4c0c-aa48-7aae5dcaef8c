# Request Management Testing Guide

This guide provides comprehensive testing scenarios for the Request Management feature.

## Prerequisites

1. Ensure the application is running
2. Have valid JWT tokens for both regular users and admin users
3. Access to GraphQL endpoint (typically `/graphql`)

## Test Scenarios

### 1. Create Request (Regular User)

**Endpoint**: `saveRequest`
**Required Permission**: `ROLE_REQUESTS_CREATE`

```graphql
mutation {
  saveRequest(requestDto: {
    title: "Vacation Request - December 2024"
    description: "Requesting 5 days vacation from December 20-24, 2024 for family holiday."
  }) {
    status
    code
    data {
      uuid
      title
      description
      status
      requestedDate
      requestedBy {
        uuid
        username
        fullName
        email
      }
    }
    errorDescription
  }
}
```

**Expected Result**: 
- `status: true`
- `code: SUCCESS`
- Request created with `PENDING` status
- `requestedBy` populated with current user info

### 2. Get My Requests (Regular User)

**Endpoint**: `getMyRequests`
**Required Permission**: `ROLE_REQUESTS_VIEW`

```graphql
query {
  getMyRequests(filter: {
    page: 0
    size: 10
    sortBy: "requestedDate"
    sortDirection: "DESC"
  }) {
    status
    code
    dataList {
      uuid
      title
      description
      status
      requestedDate
      approvedDate
      approvalComment
      requestedBy {
        fullName
      }
      approvedBy {
        fullName
      }
    }
    extras
  }
}
```

**Expected Result**:
- Only requests created by the current user
- Pagination information in `extras`
- Sorted by `requestedDate` descending

### 3. Update Request (Regular User - Own Request)

**Endpoint**: `saveRequest`
**Required Permission**: `ROLE_REQUESTS_EDIT`

```graphql
mutation {
  saveRequest(requestDto: {
    uuid: "request-uuid-from-step-1"
    title: "Updated Vacation Request - December 2024"
    description: "Updated: Requesting 7 days vacation from December 18-26, 2024 for extended family holiday."
  }) {
    status
    code
    data {
      uuid
      title
      description
      status
      updatedAt
    }
    errorDescription
  }
}
```

**Expected Result**:
- `status: true` if request is PENDING and owned by user
- Updated title and description
- `updatedAt` timestamp updated

### 4. Get All Requests (Admin User)

**Endpoint**: `getAllRequests`
**Required Permission**: `ROLE_REQUESTS_VIEW_ALL`

```graphql
query {
  getAllRequests(filter: {
    status: PENDING
    search: "vacation"
    page: 0
    size: 20
    sortBy: "requestedDate"
    sortDirection: "ASC"
  }) {
    status
    code
    dataList {
      uuid
      title
      status
      requestedDate
      requestedBy {
        username
        fullName
      }
    }
    extras
  }
}
```

**Expected Result**:
- All requests from all users (admin view)
- Filtered by status and search term
- Pagination information

### 5. Approve Request (Admin User)

**Endpoint**: `approveRequest`
**Required Permission**: `ROLE_REQUESTS_APPROVE`

```graphql
mutation {
  approveRequest(approvalDto: {
    requestUuid: "request-uuid-from-step-1"
    decision: APPROVED
    comment: "Approved for the requested dates. Please coordinate with your team lead."
  }) {
    status
    code
    data {
      uuid
      status
      approvedDate
      approvalComment
      approvedBy {
        username
        fullName
      }
    }
    errorDescription
  }
}
```

**Expected Result**:
- `status: true`
- Request status changed to `APPROVED`
- `approvedDate` set to current timestamp
- `approvedBy` populated with admin user info

### 6. Reject Request (Admin User)

```graphql
mutation {
  approveRequest(approvalDto: {
    requestUuid: "another-request-uuid"
    decision: REJECTED
    comment: "Insufficient notice period. Please submit requests at least 2 weeks in advance."
  }) {
    status
    code
    data {
      uuid
      status
      approvedDate
      approvalComment
      approvedBy {
        fullName
      }
    }
    errorDescription
  }
}
```

### 7. Delete Request (Regular User - Own Pending Request)

**Endpoint**: `deleteRequest`
**Required Permission**: `ROLE_REQUESTS_DELETE`

```graphql
mutation {
  deleteRequest(uuid: "pending-request-uuid") {
    status
    code
    data
    errorDescription
  }
}
```

**Expected Result**:
- `status: true` if request is PENDING and owned by user
- `data: "Request deleted successfully"`

## Error Testing Scenarios

### 1. Unauthorized Access

Try accessing admin endpoints with regular user token:

```graphql
query {
  getAllRequests {
    status
    code
    errorDescription
  }
}
```

**Expected**: Access denied error

### 2. Update Non-Owned Request

Try updating another user's request:

```graphql
mutation {
  saveRequest(requestDto: {
    uuid: "other-user-request-uuid"
    title: "Trying to update someone else's request"
  }) {
    status
    code
    errorDescription
  }
}
```

**Expected**: `status: false`, "You can only update your own requests"

### 3. Update Approved Request

Try updating an already approved request:

```graphql
mutation {
  saveRequest(requestDto: {
    uuid: "approved-request-uuid"
    title: "Trying to update approved request"
  }) {
    status
    code
    errorDescription
  }
}
```

**Expected**: `status: false`, "Only pending requests can be updated"

### 4. Invalid Decision in Approval

```graphql
mutation {
  approveRequest(approvalDto: {
    requestUuid: "request-uuid"
    decision: PENDING
    comment: "Invalid decision"
  }) {
    status
    code
    errorDescription
  }
}
```

**Expected**: `status: false`, "Decision must be APPROVED or REJECTED"

### 5. Validation Errors

```graphql
mutation {
  saveRequest(requestDto: {
    title: ""
    description: "A".repeat(1001)  # Exceeds max length
  }) {
    status
    code
    fieldsErrors
    errorDescription
  }
}
```

**Expected**: Validation errors in `fieldsErrors`

## Performance Testing

### 1. Pagination Test

Test with large datasets:

```graphql
query {
  getAllRequests(filter: {
    page: 0
    size: 100
  }) {
    status
    dataList {
      uuid
      title
    }
    extras
  }
}
```

### 2. Search Performance

Test search functionality:

```graphql
query {
  getAllRequests(filter: {
    search: "vacation"
    page: 0
    size: 50
  }) {
    status
    dataList {
      uuid
      title
    }
  }
}
```

## Security Testing

1. **Token Validation**: Test with expired/invalid tokens
2. **Permission Checks**: Verify each endpoint respects permission requirements
3. **Data Isolation**: Ensure users can only see their own data
4. **Input Sanitization**: Test with malicious input strings

## Notes

- Replace `request-uuid-from-step-1` with actual UUIDs from previous operations
- Ensure proper authorization headers are included in all requests
- Check the `extras` field for pagination metadata
- Monitor application logs for detailed error information
- Test both success and failure scenarios for comprehensive coverage
