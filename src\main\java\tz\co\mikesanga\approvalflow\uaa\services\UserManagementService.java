package tz.co.mikesanga.approvalflow.uaa.services;

import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.*;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

public interface UserManagementService {

    GqlResponseDto<UserResponseDto> getAllUsers(UserFilterDto filterDto);

    /**
     * Save a user (create new if uuid is null, update existing if uuid is provided)
     */
    GqlResponseDto<UserResponseDto> saveUser(UserCreateRequestDto userDto);

    GqlResponseDto<UserResponseDto> getUserByUuid(String uuid);

    GqlResponseDto<UserResponseDto> deactivateUser(String uuid);

    GqlResponseDto<UserResponseDto> reactivateUser(String uuid);

    GqlResponseDto<UserAccount> changeUserStatus(String uuid, UserAccount.UserStatus status);
}
