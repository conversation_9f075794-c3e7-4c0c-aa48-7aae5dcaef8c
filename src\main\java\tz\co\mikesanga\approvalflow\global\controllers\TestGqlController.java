package tz.co.mikesanga.approvalflow.global.controllers;


import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@GraphQLApi
@Service
public class TestGqlController {

    @GraphQLQuery(name = "testController")
    @PreAuthorize("hasRole('ROLE_DEVELOPER_DASHBOARD_VIEW')")
    public String test(){
        return "If you see this mean graphql is working";
    }
}
